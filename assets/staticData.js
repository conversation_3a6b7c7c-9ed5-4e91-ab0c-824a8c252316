import { color } from "echarts";

//数据范围
export const dataRange = [
  { text: '本人数据', value: '1' },
  { text: '部门数据', value: '2' },
  { text: '门店数据', value: '3' },
]

export const dayArray = Object.freeze([
  { text: '全部', value: '' },
  { text: '今天', value: '1' },
  { text: '昨天', value: '2' },
  { text: '本周', value: '3' },
  { text: '上周', value: '4' },
  { text: '本月', value: '5' },
  { text: '上月', value: '6' },
  { text: '今年', value: '7' },
  { text: '去年', value: '8' },
]);
export const daterang = [
  { text: '1个月', value: '30' },
  { text: '2个月', value: '60' },
  { text: '3个月', value: '90' },
  { text: '6个月', value: '180' },
  { text: '1年', value: '365' },
  { text: '2年', value: '730' },
  { text: '3年', value: '1095' },
]

export const classTypeArray = [
  { text: '销售', value: '1' },
  { text: '服务', value: '2' },
  { text: '运营', value: '3' },
  { text: '财务', value: '4' },
  { text: '其它', value: '5' },
]

// 情感状态
export const s_servofArray = [
  { text: '全部', value: '' },
  { text: '寻找中', value: '100' },
  { text: '交往中', value: '1' },
  { text: '未找到', value: '2' },
  { text: '暂停', value: '3' },
  { text: '已找到', value: '10' },
  // { text: '隐藏资料', value: '11' },
]

// 情感状态
export const servofArray = [
  { text: '寻找中', value: '0' },
  { text: '交往中', value: '1' },
  { text: '未找到', value: '2' },
  { text: '暂停', value: '3' },
  { text: '已找到', value: '10' },
  // { text: '隐藏资料', value: '11' },
]

// 签约状态
export const signflagArray = [
  { text: '未签约', value: '0', color: 'color_title3', bg: 'bg_title3' },
  { text: '未签约', value: '1', color: 'color_title3', bg: 'bg_title3' },
  { text: '已签约', value: '2', color: 'color_green', bg: 'bg_green' },
  { text: '到期未续约', value: '3', color: 'color_title3', bg: 'bg_title3' },
  { text: '已续约', value: '4', color: 'color_assist', bg: 'bg_assist' },
  { text: '嘉宾', value: '10', color: 'color_warn', bg: 'bg_warn' },
  { text: '已关单', value: '11', color: 'color_red', bg: 'bg_red' },
]

// 签约状态
export const signflag_s = [
  { text: '未签约', value: '1', },
  { text: '已签约', value: '2', },
  { text: '到期未续约', value: '3', },
  { text: '已续约', value: '4', },
  { text: '嘉宾', value: '10', },
  { text: '已关单', value: '11', },
]

// 角色
export const powerList = [
  { text: '销售总监', value: '1' },
  { text: '销售红娘', value: '2' },
  { text: '服务总监', value: '3' },
  { text: '服务红娘', value: '4' },
  { text: '前台', value: '5' },
  { text: '财务', value: '6' },
  { text: '资源分配', value: '7' },
  { text: '店长', value: '8' }
]
// 认证状态
export const rzArray = [
  { text: '已认证', value: 1 },
  { text: '未认证', value: 0 },
]
// 线上用户
export const lineUserArray = [
  { text: '线上用户', value: 1 },
  { text: '未注册用户', value: 2 },
]

// 红娘帮约
export const toStoreFlagArray = [
  { text: '待到店', value: 10, color: 'color_warn' },
  { text: '已到店', value: 1, color: 'color_success' },
  { text: '未到店', value: 2, color: 'color_title3' },
  { text: '毁单', value: 3, color: 'color_red' }
]
// 审核状态
export const statusArray = [
  { text: '待审核', value: 0, color: 'color_warn' },
  { text: '审核通过', value: 1, color: 'color_success' },
  { text: '审核未通过', value: 2, color: 'color_red' },
  { text: '已取消', value: 3, color: 'color_title3' }
]
// 审核状态
export const s_statusArray = [
  { text: '全部', value: '' },
  { text: '待审核', value: 100 },
  { text: '审核通过', value: 1 },
  { text: '审核未通过', value: 2 },
  { text: '已取消', value: 3 }
]

// 约见状态
export const s_crmmeetFlag = [
  { text: '全部', value: 0 },
  { text: '待服务', value: 1 },
  { text: '服务中', value: 2 },
  { text: '暂停', value: 3 },
  { text: '已见面', value: 10 },
  { text: '已拒绝', value: 11 },
  { text: '已取消', value: 12 }
]

// 约见状态
export const crmmeetFlag = [
  { text: '待服务', value: '0', color: 'color_warn' },
  { text: '待服务', value: '1', color: 'color_warn' },
  { text: '服务中', value: '2', color: 'color_warn' },
  { text: '暂停', value: '3', color: 'color_title3' },
  { text: '已见面', value: '10', color: 'color_success' },
  { text: '已拒绝', value: '11', color: 'color_error' },
  { text: '已取消', value: '12', color: 'color_title3' }
]

// 合同状态
export const s_orderFlag = [
  { text: '全部', value: 0, color: 'color_warn' },
  { text: '待审核', value: 4, color: 'color_warn' },
  { text: '审核通过', value: 1, color: 'color_warn' },
  { text: '审核不通过', value: 2, color: 'color_warn' },
  { text: '草稿', value: 3, color: 'color_title3' },
  { text: '取消/作废', value: 11, color: 'color_error' },
]

// 合同
export const orderFlag = [
  { text: '待审核', value: 0, color: 'color_warn' },
  { text: '通 过', value: 1, color: 'color_success' },
  { text: '不通过', value: 2, color: 'color_error' },
  { text: '草 稿', value: 3, color: 'color_title3' },
  { text: '取消/作废', value: 11, color: 'color_title3' },
  { text: '完 成', value: 10, color: 'color_success' },
]
// 收款状态
export const payStatus_t = {
  '0': { text: '待收款', color: 'color_warn', bg: 'bg_warn_015' },
  '1': { text: '部分收款', color: 'color_assist', bg: 'bg_assist_12' },
  '10': { text: '全部收款', color: 'color_success', bg: 'bg_success_015' },
  '11': { text: '部分退款', color: 'color_title3', bg: 'bg_title3_015' },
  '20': { text: '全部退款', color: 'color_red', bg: 'bg_error_015' },
}


// 电子合同通道
export const etapiType = [
  { text: 'RP电子合同', value: 'oesign' },
  { text: '电子牵', value: 'letsign', },
  { text: '法大大', value: 'fadadasign', },
]

// 跟进状态
export const followStatus = [
  { text: '到期未跟进', value: '1' },
  { text: '今天需跟进', value: '2', },

  { text: '今天已跟进', value: '8', },
  { text: '明日需跟进', value: '9', },

  { text: '3天未跟进', value: '3', },
  { text: '7天未跟进', value: '4', },
  { text: '今天坠海', value: '5', },
  { text: '明天坠海', value: '6', },
  { text: '近7天坠海', value: '7', },
]

// 嘉宾跟进状态
export const followPassuserStatus = [
  { text: '到期未跟进', value: '1' },
  { text: '今天需跟进', value: '2', },
  { text: '今天已跟进', value: '8', },
  { text: '明日需跟进', value: '9', },
  { text: '3天未跟进', value: '3', },
  { text: '7天未跟进', value: '4', },
]

export const follow_serv = [
  { text: '到期未跟进', value: '1' },
  { text: '今天需跟进', value: '2', },
  { text: '今天已跟进', value: '6', },
  { text: '明日需跟进', value: '7', },

  { text: '3天未跟进', value: '3', },
  { text: '7天未跟进', value: '4', },
  { text: '15天未跟进', value: '5', },
]

// 头像状态
export const headStatus = [
  { text: '未上传', value: '0' },
  { text: '已上传待审核', value: '2', },
  { text: '审核通过', value: '1', },
  { text: '审核不同通过', value: '3', },
]

// 用户信息状态
export const userinfoStatus = [
  { text: '待审核', value: '0' },
  { text: '审核通过', value: '1' },
  { text: '审核未通过', value: '2' },
  { text: '黑名单', value: '3' },
  { text: '找到意中人', value: '4' },
  { text: '隐藏资料', value: '5' },
]

// 谁可以看我的头像
export const maskStatus = [
  { text: '公开(任何人可见)', value: '0' },
  { text: 'VIP用户可见', value: '1' },
  { text: '实名用户可见', value: '2' },
  { text: 'VIP+实名可见', value: '3' },
  { text: '保密(仅自己可见)', value: '10' }
]

// 谁可以看我的资料
export const viewhomeStatus = [
  { text: '任何人可见', value: '0' },
  { text: 'VIP用户可见', value: '1' },
  { text: '实名用户可见', value: '2' },
  { text: 'VIP+实名可见', value: '3' },
]

// 到店状态
export const interStatus = [
  { text: '待到店', value: '0', color: 'color_warn' },
  { text: '已到店', value: '1', color: 'color_green' },
  { text: '爽约', value: '2', color: 'color_title3' },
  { text: '取消', value: '3', color: 'color_red' },
]
export const interStatus_s = [
  { text: '待到店', value: '10' },
  { text: '已到店', value: '1' },
  { text: '爽约', value: '2' },
  { text: '取消', value: '3' },
]
// 短信发送状态
export const smsStatus = [
  { text: '待发送', value: '0', color: 'color_warn' },
  { text: '发送成功', value: '1', color: 'color_green' },
  { text: '发送失败', value: '2', color: 'color_red' },
]


export const FUNNEL_TYPES = {
  SALES: '0', // 销售漏斗
  SERVICE: '1' // 服务漏斗
};

export const funnelStatus = Object.freeze([
  { value: FUNNEL_TYPES.SALES, text: '销售漏斗' },
  { value: FUNNEL_TYPES.SERVICE, text: '服务漏斗' }
]);


export const sortlist = [
  { text: '录入时间', value: 'id' },
  { text: '分配时间', value: 'allottime' },
  { text: '年龄排序', value: 'age' },
  { text: '学历排序', value: 'education' },
  { text: '年收入排序', value: 'salary' }
];

export const whiteboardOptions = [
  { text: '不限', value: '' },
  { text: '是', value: '1' },
  { text: '否', value: '2' }
];

export const afterOptions = [
  { text: '不限', value: '' },
  { text: '有', value: '1' },
  { text: '无', value: '2' }
];

export const genderOptions = [
  { text: '男', value: '1', icon: { text: 'iconfont icon-nan', color: '#2C5CE1' } },
  { text: '女', value: '2', icon: { text: 'iconfont icon-nan', color: '#FF64A0' } },
  { text: '未知', value: '' },
];
