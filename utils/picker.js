import http from '@/utils/api.js';

const MODULE = 'vuewap';

const storeDataToLocal = (storageKey, data) => {
	if (!data || typeof data !== 'object') return;
	for (const [key, value] of Object.entries(data)) {
		uni.setStorageSync(key, JSON.stringify(value));
	}
};

const fetchPickerData = async (action, storageKey) => {
	const res = await http.post('', {
		m: MODULE,
		c: 'picker',
		a: action
	});
	if (res.ret === 1) {
		uni.setStorageSync(storageKey, JSON.stringify(res.result));
	}
	return res;
};

async function oePicker(callback) {
	try {
		// 并行处理初始请求
		const [allVarRes, widgetRes, makerRes] = await Promise.all([
			http.post('', { m: MODULE, c: 'picker', a: 'allvar' }),
			http.post('', { m: MODULE, c: 'picker', a: 'widget', item: 'all' }),
		]);

		// 统一处理存储逻辑
		storeDataToLocal('allvar', allVarRes.result);
		storeDataToLocal('widget', widgetRes.result);


		if (widgetRes.ret === 1) {
			uni.setStorageSync('sign', JSON.stringify([
				{ text: '未签约', value: 1 },
				{ text: '已签约', value: 2 },
				{ text: '被动方', value: 10 },
				{ text: '关单', value: 11 }
			]));
		}


		await Promise.all([
			fetchPickerData('area', 'district'),
			fetchPickerData('hometown', 'hometown')
		]);

		callback?.();
	} catch (error) {
		console.error(error);
	}
}

export default oePicker;