<template>
  <view class="home">
    <view class="home_title_row">我的资源</view>
  </view>

  <CustomTabBar :current="1" />
</template>

<script setup>
  import { ref, reactive, onMounted, computed, onUnmounted, watch } from 'vue';

  import CustomTabBar from '@/oeui/new/CustomTabBar';
</script>

<style lang="scss" scoped>
  .home {
    width: 100%;
    display: flex;
    flex-direction: column;
    padding-bottom: 100rpx;

    &_title_row {
      @include flex-center;
      height: 92rpx;
      background: #6365e0;
      color: #fff;
      font-size: 36rpx;
      font-weight: 500;
    }
  }
</style>
